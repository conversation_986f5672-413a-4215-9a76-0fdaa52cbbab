Stack trace:
Frame         Function      Args
0007FFFFA910  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9810) msys-2.0.dll+0x1FE8E
0007FFFFA910  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABE8) msys-2.0.dll+0x67F9
0007FFFFA910  000210046832 (000210286019, 0007FFFFA7C8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA910  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA910  000210068E24 (0007FFFFA920, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABF0  00021006A225 (0007FFFFA920, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA6E6B0000 ntdll.dll
7FFA6DEF0000 KERNEL32.DLL
7FFA6BA90000 KERNELBASE.dll
7FFA6E110000 USER32.dll
7FFA6C3B0000 win32u.dll
7FFA6DFE0000 GDI32.dll
7FFA6BE40000 gdi32full.dll
7FFA6C310000 msvcp_win.dll
7FFA6BFE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA6D920000 advapi32.dll
7FFA6DC70000 msvcrt.dll
7FFA6D870000 sechost.dll
7FFA6C2E0000 bcrypt.dll
7FFA6DAC0000 RPCRT4.dll
7FFA6B1E0000 CRYPTBASE.DLL
7FFA6BF60000 bcryptPrimitives.dll
7FFA6E470000 IMM32.DLL
